package com.example.aimusicplayer.ui.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.view.animation.LinearInterpolator
import androidx.core.content.res.ResourcesCompat
import com.example.aimusicplayer.BuildConfig
import com.example.aimusicplayer.R
import com.example.aimusicplayer.utils.ImageUtils

/**
 * 简化版黑胶唱片专辑封面视图
 * 只保留黑胶唱片旋转动画效果，移除唱臂相关功能
 */
class AlbumCoverView @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 黑胶唱片背景
    private var discBitmap: Bitmap? = null
    private val discMatrix by lazy { Matrix() }
    private val discStartPoint by lazy { Point() } // 图片起始坐标
    private val discCenterPoint by lazy { Point() } // 旋转中心坐标
    private var discRotation = 0.0f

    // 专辑封面
    private var coverBitmap: Bitmap? = null
    private val coverMatrix by lazy { Matrix() }
    private val coverStartPoint by lazy { Point() }
    private val coverCenterPoint by lazy { Point() }
    private var coverSize = 0

    // 绘制相关
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val coverRect = RectF()

    // 动画相关
    private val coverBorder: Drawable by lazy {
        ResourcesCompat.getDrawable(resources, R.drawable.bg_playing_cover_border, null)!!
    }

    private val rotationAnimator by lazy {
        ValueAnimator.ofFloat(0f, 360f).apply {
            duration = 20000 // 20秒一圈，符合真实黑胶转速
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            addUpdateListener(rotationUpdateListener)
        }
    }

    // 状态
    private var isPlaying = false
    private var lastRotation = 0f // 记录上次旋转角度，用于恢复旋转

    init {
        // 初始化画笔
        circlePaint.color = 0xFF111111.toInt() // 黑色
        circlePaint.style = Paint.Style.FILL

        // 设置混合模式，用于绘制圆形封面
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

        // 初始化位图资源
        initBitmaps()
    }

    /**
     * 初始化位图资源
     * 安全地从资源创建Bitmap，支持Vector Drawable
     */
    private fun initBitmaps() {
        try {
            Log.d("AlbumCoverView", "开始初始化位图资源")

            // 初始化黑胶唱片背景
            discBitmap = createBitmapFromResource(R.drawable.bg_playing_disc)
            Log.d("AlbumCoverView", "黑胶唱片背景加载: ${if (discBitmap != null) "成功" else "失败"}")

        } catch (e: Exception) {
            Log.e("AlbumCoverView", "初始化位图资源失败", e)
            // 创建默认位图以防止崩溃
            createDefaultBitmaps()
        }
    }



    /**
     * 从资源创建Bitmap，支持Vector Drawable
     */
    private fun createBitmapFromResource(resourceId: Int): Bitmap? {
        return try {
            val drawable = ResourcesCompat.getDrawable(resources, resourceId, null)
            drawable?.let { ImageUtils.drawableToBitmap(it) }
        } catch (e: Exception) {
            android.util.Log.e("AlbumCoverView", "从资源创建Bitmap失败: $resourceId", e)
            null
        }
    }

    /**
     * 创建默认位图以防止崩溃
     */
    private fun createDefaultBitmaps() {
        try {
            // 创建默认的黑胶唱片背景（简单的圆形）
            if (discBitmap == null) {
                discBitmap = createDefaultDiscBitmap()
            }
        } catch (e: Exception) {
            android.util.Log.e("AlbumCoverView", "创建默认位图失败", e)
        }
    }

    /**
     * 创建默认的黑胶唱片背景
     */
    private fun createDefaultDiscBitmap(): Bitmap {
        val size = 200
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        // 绘制黑色圆形
        paint.color = 0xFF333333.toInt()
        canvas.drawCircle(size / 2f, size / 2f, size / 2f, paint)

        // 绘制中心小圆
        paint.color = 0xFF666666.toInt()
        canvas.drawCircle(size / 2f, size / 2f, size / 8f, paint)

        return bitmap
    }



    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            initSize()
        }
    }

    /**
     * 确定图片起始坐标与旋转中心坐标
     * 简化版本，只处理黑胶唱片和专辑封面
     */
    private fun initSize() {
        val unit = width.coerceAtMost(height) / 10

        // 1. 初始化黑胶唱片 - 居中显示
        discBitmap?.let { disc ->
            discBitmap = ImageUtils.resizeImage(disc, unit * 6, unit * 6)
        }

        val discWidth = discBitmap?.width ?: (unit * 6)
        val discHeight = discBitmap?.height ?: (unit * 6)

        // 唱片位置：水平垂直居中
        discStartPoint.x = (width - discWidth) / 2
        discStartPoint.y = (height - discHeight) / 2
        discCenterPoint.x = discStartPoint.x + discWidth / 2
        discCenterPoint.y = discStartPoint.y + discHeight / 2

        // 2. 初始化专辑封面 - 保持与黑胶唱片的比例关系
        coverSize = (discWidth * 0.65).toInt()  // 封面直径为唱片的65%
        coverStartPoint.x = discStartPoint.x + (discWidth - coverSize) / 2
        coverStartPoint.y = discStartPoint.y + (discHeight - coverSize) / 2
        coverCenterPoint.x = discCenterPoint.x
        coverCenterPoint.y = discCenterPoint.y

        Log.d("AlbumCoverView", "黑胶唱片尺寸: ${discWidth}x${discHeight}")
        Log.d("AlbumCoverView", "黑胶唱片位置: (${discStartPoint.x}, ${discStartPoint.y})")
        Log.d("AlbumCoverView", "旋转中心: (${discCenterPoint.x}, ${discCenterPoint.y})")
    }

    override fun onDraw(canvas: Canvas) {
        try {
            // 检查画布有效性
            if (width <= 0 || height <= 0) {
                return
            }

            // 保存画布状态
            val saveCount = canvas.save()

            // 1. 绘制专辑封面（圆形裁剪，在黑胶里面）
            val cover = coverBitmap
            if (cover != null && !cover.isRecycled) {
                try {
                    // 保存画布状态用于裁剪
                    val clipSaveCount = canvas.save()

                    // 创建圆形裁剪路径
                    val clipPath = Path()
                    clipPath.addCircle(
                        coverCenterPoint.x.toFloat(),
                        coverCenterPoint.y.toFloat(),
                        coverSize / 2f,
                        Path.Direction.CW
                    )
                    canvas.clipPath(clipPath)

                    // 绘制旋转的专辑封面
                    coverMatrix.reset()
                    coverMatrix.setRotate(
                        discRotation, // 封面跟随唱片旋转
                        coverCenterPoint.x.toFloat(),
                        coverCenterPoint.y.toFloat()
                    )
                    coverMatrix.preTranslate(coverStartPoint.x.toFloat(), coverStartPoint.y.toFloat())
                    coverMatrix.preScale(
                        coverSize.toFloat() / cover.width,
                        coverSize.toFloat() / cover.height
                    )
                    canvas.drawBitmap(cover, coverMatrix, null)

                    // 恢复裁剪状态
                    canvas.restoreToCount(clipSaveCount)
                } catch (e: Exception) {
                    Log.e("AlbumCoverView", "绘制专辑封面失败", e)
                }
            }

            // 2. 绘制黑胶唱片外侧半透明边框
            try {
                discBitmap?.let { disc ->
                    coverBorder.setBounds(
                        discStartPoint.x - COVER_BORDER_WIDTH,
                        discStartPoint.y - COVER_BORDER_WIDTH,
                        discStartPoint.x + disc.width + COVER_BORDER_WIDTH,
                        discStartPoint.y + disc.height + COVER_BORDER_WIDTH
                    )
                    coverBorder.draw(canvas)
                }
            } catch (e: Exception) {
                Log.e("AlbumCoverView", "绘制边框失败", e)
            }

            // 3. 绘制黑胶唱片（在封面上面，形成遮罩效果）
            discBitmap?.let { disc ->
                if (!disc.isRecycled) {
                    try {
                        discMatrix.reset()
                        // 设置旋转中心和旋转角度，setRotate和preTranslate顺序很重要
                        discMatrix.setRotate(
                            discRotation,
                            discCenterPoint.x.toFloat(),
                            discCenterPoint.y.toFloat()
                        )
                        // 设置图片起始坐标
                        discMatrix.preTranslate(discStartPoint.x.toFloat(), discStartPoint.y.toFloat())
                        canvas.drawBitmap(disc, discMatrix, null)
                    } catch (e: Exception) {
                        Log.e("AlbumCoverView", "绘制黑胶唱片失败", e)
                    }
                }
            }



            // 恢复画布状态
            canvas.restoreToCount(saveCount)
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "onDraw执行失败", e)
        }
    }



    /**
     * 设置专辑封面 - 优化版本，支持缓存和错误处理
     * @param bitmap 封面图片
     */
    fun setCoverBitmap(bitmap: Bitmap?) {
        try {
            // 释放之前的封面资源
            if (coverBitmap != null && coverBitmap != bitmap && !coverBitmap!!.isRecycled) {
                // 不立即回收，让系统GC处理
                coverBitmap = null
            }

            coverBitmap = bitmap

            // 优化：只在视图可见时重绘
            if (isShown && visibility == View.VISIBLE) {
                invalidate()
            }

            Log.d("AlbumCoverView", "专辑封面设置成功: ${bitmap?.width}x${bitmap?.height}")
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "设置专辑封面失败", e)
        }
    }

    /**
     * 开始播放动画 - 优化版本，支持从暂停位置恢复
     */
    fun start() {
        if (isPlaying) {
            return
        }
        isPlaying = true

        // 如果动画已经暂停，从当前位置恢复
        if (rotationAnimator.isPaused) {
            rotationAnimator.resume()
        } else {
            // 从当前角度开始新的动画
            val currentRotation = discRotation % 360f
            rotationAnimator.setFloatValues(currentRotation, currentRotation + 360f)
            rotationAnimator.start()
        }

        Log.d("AlbumCoverView", "开始播放动画，当前角度: $discRotation")
    }

    /**
     * 暂停播放动画 - 保持当前角度
     */
    fun pause() {
        if (!isPlaying) {
            return
        }
        isPlaying = false
        lastRotation = discRotation % 360f // 保存当前角度
        rotationAnimator.pause()

        Log.d("AlbumCoverView", "暂停播放动画，保存角度: $lastRotation")
    }

    /**
     * 重置动画状态
     */
    fun reset() {
        isPlaying = false
        discRotation = 0.0f
        rotationAnimator.cancel()
        invalidate()
    }

    /**
     * 切换歌曲效果 - 简化版
     * 只重置旋转角度
     */
    fun switchTrack() {
        discRotation = 0.0f
        invalidate()
    }

    /**
     * 唱片旋转动画更新监听器
     * 优化性能，减少不必要的重绘，确保>30fps
     */
    private val rotationUpdateListener = AnimatorUpdateListener { animation ->
        try {
            val newRotation = animation.animatedValue as Float

            // 优化：只有角度变化超过阈值才重绘，确保流畅度
            if (abs(newRotation - discRotation) > 0.5f || !isShown) {
                discRotation = newRotation
                // 优化：只在视图可见时重绘
                if (isShown && visibility == View.VISIBLE) {
                    invalidate()
                }
            }
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "旋转动画更新失败", e)
        }
    }

    /**
     * 设置硬件加速和性能优化（优化版）
     * 提高动画流畅度，解决OpenGL渲染问题
     */
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        try {
            // 智能选择渲染模式，避免OpenGL问题
            if (isHardwareAccelerationSupported()) {
                setLayerType(LAYER_TYPE_HARDWARE, null)
                Log.d("AlbumCoverView", "硬件加速已启用")
            } else {
                setLayerType(LAYER_TYPE_SOFTWARE, null)
                Log.d("AlbumCoverView", "使用软件渲染模式")
            }

            // 优化视图属性
            setWillNotDraw(false) // 确保onDraw会被调用

        } catch (e: Exception) {
            Log.e("AlbumCoverView", "渲染模式设置失败，使用默认模式", e)
            // 如果设置失败，使用默认模式
            setLayerType(LAYER_TYPE_NONE, null)
        }
    }

    /**
     * 检查硬件加速支持情况
     */
    private fun isHardwareAccelerationSupported(): Boolean {
        return try {
            // 检查当前设备是否支持硬件加速
            val activity = context as? android.app.Activity
            activity?.window?.attributes?.flags?.and(
                android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            ) != 0
        } catch (e: Exception) {
            Log.w("AlbumCoverView", "无法检查硬件加速支持", e)
            false
        }
    }

    /**
     * 清理资源，防止内存泄漏
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        try {
            // 停止所有动画
            rotationAnimator.cancel()

            // 清理位图资源
            discBitmap?.let {
                if (!it.isRecycled) {
                    // 不要手动回收，让GC处理
                    Log.d("AlbumCoverView", "位图资源已标记清理")
                }
            }

            Log.d("AlbumCoverView", "视图资源已清理")
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "清理资源时发生错误", e)
        }
    }



    companion object {
        private const val COVER_BORDER_WIDTH = 6
    }
}
